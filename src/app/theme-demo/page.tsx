"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { ThemeToggle, useTheme } from "@/components/theme-provider";
import { scholarlyGradients, scholarlyTheme } from "@/lib/theme";
import { 
  BookOpen, 
  TrendingUp, 
  Award, 
  Calendar,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Info
} from "lucide-react";

export default function ThemeDemoPage() {
  const { resolvedTheme } = useTheme();

  const colors = scholarlyTheme[resolvedTheme];

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-4xl font-bold text-foreground mb-2">
              Scholarly Serenity Theme
            </h1>
            <p className="text-muted-foreground text-lg">
              Professional and calming theme for CSS exam preparation
            </p>
          </div>
          <ThemeToggle />
        </div>

        <Separator />

        {/* Color Palette */}
        <section>
          <h2 className="text-2xl font-semibold text-foreground mb-6">Color Palette</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Primary Colors */}
            <Card>
              <CardHeader>
                <CardTitle className="text-primary">Primary Colors</CardTitle>
                <CardDescription>Navy Blue - Trust & Authority</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 rounded bg-primary"></div>
                  <span className="text-sm font-mono">{colors.primary.main}</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 rounded bg-primary/80"></div>
                  <span className="text-sm font-mono text-muted-foreground">Primary 80%</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 rounded bg-primary/60"></div>
                  <span className="text-sm font-mono text-muted-foreground">Primary 60%</span>
                </div>
              </CardContent>
            </Card>

            {/* Secondary Colors */}
            <Card>
              <CardHeader>
                <CardTitle className="text-secondary">Secondary Colors</CardTitle>
                <CardDescription>Teal - Modern & Approachable</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 rounded bg-secondary"></div>
                  <span className="text-sm font-mono">{colors.secondary.main}</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 rounded bg-secondary/80"></div>
                  <span className="text-sm font-mono text-muted-foreground">Secondary 80%</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 rounded bg-secondary/60"></div>
                  <span className="text-sm font-mono text-muted-foreground">Secondary 60%</span>
                </div>
              </CardContent>
            </Card>

            {/* Success Colors */}
            <Card>
              <CardHeader>
                <CardTitle className="text-success">Success Colors</CardTitle>
                <CardDescription>Lime Green - Progress & Achievement</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 rounded bg-success"></div>
                  <span className="text-sm font-mono">{colors.success.main}</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 rounded bg-success/80"></div>
                  <span className="text-sm font-mono text-muted-foreground">Success 80%</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 rounded bg-success/60"></div>
                  <span className="text-sm font-mono text-muted-foreground">Success 60%</span>
                </div>
              </CardContent>
            </Card>

            {/* Warning Colors */}
            <Card>
              <CardHeader>
                <CardTitle className="text-warning">Warning Colors</CardTitle>
                <CardDescription>Amber - Attention & Alerts</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 rounded bg-warning"></div>
                  <span className="text-sm font-mono">{colors.warning.main}</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 rounded bg-warning/80"></div>
                  <span className="text-sm font-mono text-muted-foreground">Warning 80%</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 rounded bg-warning/60"></div>
                  <span className="text-sm font-mono text-muted-foreground">Warning 60%</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* Gradients */}
        <section>
          <h2 className="text-2xl font-semibold text-foreground mb-6">Gradients</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Primary Gradient</CardTitle>
                <CardDescription>Navy to Teal</CardDescription>
              </CardHeader>
              <CardContent>
                <div 
                  className="h-20 rounded-lg mb-4"
                  style={{ background: scholarlyGradients.primary }}
                ></div>
                <p className="text-sm text-muted-foreground">
                  Used for main CTAs and important elements
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Success Gradient</CardTitle>
                <CardDescription>Lime Green</CardDescription>
              </CardHeader>
              <CardContent>
                <div 
                  className="h-20 rounded-lg mb-4"
                  style={{ background: scholarlyGradients.success }}
                ></div>
                <p className="text-sm text-muted-foreground">
                  Used for progress indicators and achievements
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Warning Gradient</CardTitle>
                <CardDescription>Amber</CardDescription>
              </CardHeader>
              <CardContent>
                <div 
                  className="h-20 rounded-lg mb-4"
                  style={{ background: scholarlyGradients.warning }}
                ></div>
                <p className="text-sm text-muted-foreground">
                  Used for alerts and important notices
                </p>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* UI Components */}
        <section>
          <h2 className="text-2xl font-semibold text-foreground mb-6">UI Components</h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Buttons */}
            <Card>
              <CardHeader>
                <CardTitle>Buttons</CardTitle>
                <CardDescription>Various button styles and states</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex flex-wrap gap-3">
                  <Button>Primary Button</Button>
                  <Button variant="secondary">Secondary Button</Button>
                  <Button variant="outline">Outline Button</Button>
                  <Button variant="ghost">Ghost Button</Button>
                </div>
                <div className="flex flex-wrap gap-3">
                  <Button size="sm">Small</Button>
                  <Button size="default">Default</Button>
                  <Button size="lg">Large</Button>
                </div>
                <div className="flex flex-wrap gap-3">
                  <Button disabled>Disabled</Button>
                  <Button className="bg-success hover:bg-success/90">Success</Button>
                  <Button className="bg-warning hover:bg-warning/90">Warning</Button>
                  <Button className="bg-destructive hover:bg-destructive/90">Destructive</Button>
                </div>
              </CardContent>
            </Card>

            {/* Badges and Status */}
            <Card>
              <CardHeader>
                <CardTitle>Badges & Status</CardTitle>
                <CardDescription>Status indicators and labels</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex flex-wrap gap-2">
                  <Badge>Default</Badge>
                  <Badge variant="secondary">Secondary</Badge>
                  <Badge variant="outline">Outline</Badge>
                </div>
                <div className="flex flex-wrap gap-2">
                  <Badge className="bg-success text-success-foreground">
                    <CheckCircle className="w-3 h-3 mr-1" />
                    Completed
                  </Badge>
                  <Badge className="bg-warning text-warning-foreground">
                    <AlertTriangle className="w-3 h-3 mr-1" />
                    Pending
                  </Badge>
                  <Badge className="bg-destructive text-destructive-foreground">
                    <XCircle className="w-3 h-3 mr-1" />
                    Failed
                  </Badge>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 rounded-full bg-success"></div>
                    <span className="text-sm">Online</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 rounded-full bg-warning"></div>
                    <span className="text-sm">Away</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 rounded-full bg-muted-foreground"></div>
                    <span className="text-sm">Offline</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* Progress and Stats */}
        <section>
          <h2 className="text-2xl font-semibold text-foreground mb-6">Progress & Statistics</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Tests Completed</CardTitle>
                <BookOpen className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-success">12</div>
                <p className="text-xs text-muted-foreground">
                  +2 from last week
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Average Score</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-secondary">78%</div>
                <p className="text-xs text-muted-foreground">
                  +5% from last month
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Study Streak</CardTitle>
                <Calendar className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-warning">7 days</div>
                <p className="text-xs text-muted-foreground">
                  Keep it up!
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Achievements</CardTitle>
                <Award className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-primary">15</div>
                <p className="text-xs text-muted-foreground">
                  +3 this month
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Progress Bar */}
          <Card className="mt-6">
            <CardHeader>
              <CardTitle>Current Progress</CardTitle>
              <CardDescription>Overall CSS preparation progress</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>General Knowledge</span>
                  <span>85%</span>
                </div>
                <Progress value={85} className="h-2" />
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Current Affairs</span>
                  <span>72%</span>
                </div>
                <Progress value={72} className="h-2" />
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Pakistan Affairs</span>
                  <span>91%</span>
                </div>
                <Progress value={91} className="h-2" />
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Islamic Studies</span>
                  <span>68%</span>
                </div>
                <Progress value={68} className="h-2" />
              </div>
            </CardContent>
          </Card>
        </section>

        {/* Typography */}
        <section>
          <h2 className="text-2xl font-semibold text-foreground mb-6">Typography</h2>
          <Card>
            <CardContent className="space-y-4 pt-6">
              <div>
                <h1 className="text-4xl font-bold text-foreground">Heading 1 - Main Title</h1>
                <p className="text-muted-foreground">Used for page titles and main headings</p>
              </div>
              <div>
                <h2 className="text-2xl font-semibold text-foreground">Heading 2 - Section Title</h2>
                <p className="text-muted-foreground">Used for section headings</p>
              </div>
              <div>
                <h3 className="text-xl font-medium text-foreground">Heading 3 - Subsection</h3>
                <p className="text-muted-foreground">Used for subsections and card titles</p>
              </div>
              <div>
                <p className="text-base text-foreground">Body text - This is the default paragraph text used for most content. It should be easily readable and have good contrast with the background.</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Small text - Used for captions, metadata, and secondary information.</p>
              </div>
              <div>
                <code className="text-sm bg-muted px-2 py-1 rounded font-mono">Code text - Used for code snippets and technical terms.</code>
              </div>
            </CardContent>
          </Card>
        </section>

        {/* Accessibility Info */}
        <section>
          <Card className="border-primary/20 bg-primary/5">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Info className="h-5 w-5 text-primary" />
                Accessibility Features
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <h4 className="font-semibold mb-2">Contrast Ratios</h4>
                  <ul className="space-y-1 text-muted-foreground">
                    <li>• Primary text: 9.5:1 (Excellent)</li>
                    <li>• Secondary text: 7.2:1 (Good)</li>
                    <li>• UI elements: 4.5:1+ (WCAG AA compliant)</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Color Blindness Support</h4>
                  <ul className="space-y-1 text-muted-foreground">
                    <li>• Avoids red-green combinations</li>
                    <li>• Uses teal and lime for differentiation</li>
                    <li>• Icons accompany color indicators</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </section>
      </div>
    </div>
  );
}
