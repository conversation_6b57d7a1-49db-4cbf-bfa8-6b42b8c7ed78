"use client";

import * as React from "react";
import Link from "next/link";
import Image from "next/image";
import { usePathname } from "next/navigation";
import { useAuth } from "@/components/auth-provider";
import { useTheme } from "@/components/theme-provider";
import {
  LayoutDashboard,
  BookOpen,
  FileText,
  TrendingUp,
  MessageSquare,
  FolderOpen,
  Settings,
  LogOut,
  User,
  Target,
  Calendar,
  Award,
  BarChart3,
} from "lucide-react";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarInput,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
  SidebarTrigger,
} from "@/components/ui/sidebar";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";

interface SidebarProps {
  className?: string;
}

export function DashboardSidebar({ className }: SidebarProps) {
  const pathname = usePathname();
  const { user, signOut } = useAuth();
  const { resolvedTheme } = useTheme();

  const navigation = [
    {
      title: "Dashboard",
      href: "/dashboard",
      icon: LayoutDashboard,
    },
    {
      title: "Subjects",
      href: "/subjects",
      icon: BookOpen,
    },
    {
      title: "Mock Tests",
      href: "/tests",
      icon: FileText,
    },
    {
      title: "Progress",
      href: "/progress",
      icon: TrendingUp,
    },
    {
      title: "Forum",
      href: "/forum",
      icon: MessageSquare,
    },
    {
      title: "Study Materials",
      href: "/materials",
      icon: FolderOpen,
    },
  ];

  const handleSignOut = async () => {
    await signOut();
  };

  const getUserInitials = (name?: string, email?: string) => {
    if (name) {
      return name
        .split(" ")
        .map((n) => n[0])
        .join("")
        .toUpperCase()
        .slice(0, 2);
    }
    if (email) {
      return email.slice(0, 2).toUpperCase();
    }
    return "U";
  };

  const maskEmail = (email?: string) => {
    if (!email) return "<EMAIL>";
    const [username, domain] = email.split("@");
    if (username.length <= 2) return email;
    const maskedUsername = username[0] + "*".repeat(username.length - 2) + username[username.length - 1];
    return `${maskedUsername}@${domain}`;
  };

  const getDisplayName = (name?: string) => {
    if (!name) return "User";
    const firstName = name.split(" ")[0];
    return firstName.length > 10 ? firstName.slice(0, 10) + "..." : firstName;
  };

  // Choose logo based on theme for better contrast
  const logoSrc = resolvedTheme === "dark" 
    ? "/images/logos/logo-white.png" 
    : "/images/logos/logo-dark.png";

  return (
    <Sidebar
      collapsible="icon"
      className={className}
    >
      <SidebarHeader className="border-b border-sidebar-border px-2 py-2">
        <div className="flex items-center justify-between">
          <div className="relative w-16 h-16 group-data-[collapsible=icon]:w-12 group-data-[collapsible=icon]:h-16 transition-all duration-300 ease-in-out">
            <Image
              src={logoSrc}
              alt="CSS Dost Logo"
              width={64}
              height={64}
              className="w-full h-full object-contain transition-all duration-300 ease-in-out"
            />
          </div>
          <SidebarTrigger
            className="h-8 w-8 hover:bg-sidebar-accent rounded-md transition-all duration-200 ease-in-out"
          />
        </div>
      </SidebarHeader>
      <SidebarContent className="px-4">
        <SidebarGroup className="group-data-[collapsible=icon]:hidden">
          <SidebarGroupLabel className="text-xs font-semibold text-sidebar-foreground/60 uppercase tracking-wider mb-2">
            Search
          </SidebarGroupLabel>
          <SidebarInput
            placeholder="Search subjects, tests..."
            className="bg-sidebar-accent/50 border-sidebar-border focus:bg-sidebar-accent"
          />
        </SidebarGroup>

        <SidebarGroup className="mt-6">
          <SidebarGroupLabel className="text-xs font-semibold text-sidebar-foreground/60 uppercase tracking-wider mb-2">
            Navigation
          </SidebarGroupLabel>
          <SidebarMenu className="space-y-1">
            {navigation.map((item) => {
              const isActive = pathname === item.href || pathname.startsWith(item.href);
              return (
                <SidebarMenuItem key={item.href}>
                  <SidebarMenuButton
                    asChild
                    isActive={isActive}
                    className={`
                      relative rounded-lg transition-all duration-200 hover:bg-sidebar-accent
                      ${isActive
                        ? 'bg-sidebar-accent text-sidebar-accent-foreground'
                        : 'text-sidebar-foreground hover:text-sidebar-accent-foreground hover:bg-sidebar-accent/50'
                      }
                    `}
                  >
                    <Link href={item.href} className="flex items-center gap-3 px-3 py-2.5">
                      <item.icon className="w-5 h-5" />
                      <span className="font-medium group-data-[collapsible=icon]:hidden">
                        {item.title}
                      </span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              );
            })}
          </SidebarMenu>
        </SidebarGroup>
        <SidebarGroup className="mt-6 group-data-[collapsible=icon]:hidden">
          <SidebarGroupLabel className="text-xs font-semibold text-sidebar-foreground/60 uppercase tracking-wider mb-3">
            Quick Stats
          </SidebarGroupLabel>
          <div className="space-y-3">
            <div className="bg-gradient-to-r from-success/10 to-success/20 rounded-lg p-3 border border-success/20">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-success/20 rounded-lg">
                  <BarChart3 className="w-4 h-4 text-success" />
                </div>
                <div>
                  <p className="text-sm font-semibold text-sidebar-foreground">12</p>
                  <p className="text-xs text-sidebar-foreground/60">Tests Completed</p>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-r from-secondary/10 to-secondary/20 rounded-lg p-3 border border-secondary/20">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-secondary/20 rounded-lg">
                  <TrendingUp className="w-4 h-4 text-secondary" />
                </div>
                <div>
                  <p className="text-sm font-semibold text-sidebar-foreground">78%</p>
                  <p className="text-xs text-sidebar-foreground/60">Average Score</p>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-r from-warning/10 to-warning/20 rounded-lg p-3 border border-warning/20">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-warning/20 rounded-lg">
                  <Calendar className="w-4 h-4 text-warning" />
                </div>
                <div>
                  <p className="text-sm font-semibold text-sidebar-foreground">7 days</p>
                  <p className="text-xs text-sidebar-foreground/60">Study Streak</p>
                </div>
              </div>
            </div>
          </div>
        </SidebarGroup>
      </SidebarContent>
        <SidebarFooter className="border-t border-sidebar-border p-4">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="w-full justify-start h-auto p-3 rounded-lg hover:bg-sidebar-accent transition-colors group-data-[collapsible=icon]:justify-center"
              >
                <Avatar className="h-10 w-10 ring-2 ring-sidebar-border group-data-[collapsible=icon]:h-10 group-data-[collapsible=icon]:w-10 transition-all duration-300 ease-in-out">
                  <AvatarImage src={user?.user_metadata?.avatar_url} />
                  <AvatarFallback className="bg-gradient-to-br from-primary to-secondary text-white font-semibold">
                    {getUserInitials(user?.user_metadata?.name, user?.email)}
                  </AvatarFallback>
                </Avatar>
                <div className="ml-3 text-left flex-1 min-w-0 group-data-[collapsible=icon]:hidden">
                  <p className="text-sm font-semibold text-sidebar-foreground truncate">
                    {getDisplayName(user?.user_metadata?.name)}
                  </p>
                  <p className="text-xs text-sidebar-foreground/60 truncate">
                    {maskEmail(user?.email)}
                  </p>
                </div>
                <Settings className="w-4 h-4 text-sidebar-foreground/40 ml-2 group-data-[collapsible=icon]:hidden" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              className="w-56 !bg-card border border-border shadow-xl backdrop-blur-sm z-[100]"
              align="end"
              forceMount
            >
              <DropdownMenuLabel className="font-normal">
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium leading-none text-card-foreground">
                    {user?.user_metadata?.name || "User"}
                  </p>
                  <p className="text-xs leading-none text-muted-foreground">
                    {maskEmail(user?.email)}
                  </p>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="bg-card hover:bg-accent text-card-foreground cursor-pointer">
                <User className="mr-2 h-4 w-4" />
                <span>Profile</span>
              </DropdownMenuItem>
              <DropdownMenuItem className="bg-card hover:bg-accent text-card-foreground cursor-pointer">
                <Target className="mr-2 h-4 w-4" />
                <span>Goals</span>
              </DropdownMenuItem>
              <DropdownMenuItem className="bg-card hover:bg-accent text-card-foreground cursor-pointer">
                <Calendar className="mr-2 h-4 w-4" />
                <span>Schedule</span>
              </DropdownMenuItem>
              <DropdownMenuItem className="bg-card hover:bg-accent text-card-foreground cursor-pointer">
                <Award className="mr-2 h-4 w-4" />
                <span>Achievements</span>
              </DropdownMenuItem>
              <DropdownMenuSeparator className="bg-border" />
              <DropdownMenuItem className="bg-card hover:bg-accent text-card-foreground cursor-pointer">
                <Settings className="mr-2 h-4 w-4" />
                <span>Settings</span>
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={handleSignOut}
                className="bg-card hover:bg-destructive/10 cursor-pointer text-destructive hover:text-destructive/80"
              >
                <LogOut className="mr-2 h-4 w-4" />
                <span>Log out</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </SidebarFooter>
        <SidebarRail />
      </Sidebar>
  );
}
