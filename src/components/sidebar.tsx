"use client";

import * as React from "react";
import Link from "next/link";
import Image from "next/image";
import { usePathname } from "next/navigation";
import { useAuth } from "@/components/auth-provider";
import { useTheme } from "@/components/theme-provider";
import {
  LayoutDashboard,
  BookOpen,
  FileText,
  TrendingUp,
  MessageSquare,
  FolderOpen,
  Settings,
  LogOut,
  User,
  Target,
  Calendar,
  Award,
  BarChart3,
} from "lucide-react";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarInput,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
  SidebarTrigger,
} from "@/components/ui/sidebar";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";

interface SidebarProps {
  className?: string;
}

export function DashboardSidebar({ className }: SidebarProps) {
  const pathname = usePathname();
  const { user, signOut } = useAuth();
  const { resolvedTheme } = useTheme();

  const navigation = [
    {
      title: "Dashboard",
      href: "/dashboard",
      icon: LayoutDashboard,
    },
    {
      title: "Subjects",
      href: "/subjects",
      icon: BookOpen,
    },
    {
      title: "Mock Tests",
      href: "/tests",
      icon: FileText,
    },
    {
      title: "Progress",
      href: "/progress",
      icon: TrendingUp,
    },
    {
      title: "Forum",
      href: "/forum",
      icon: MessageSquare,
    },
    {
      title: "Study Materials",
      href: "/materials",
      icon: FolderOpen,
    },
  ];

  const handleSignOut = async () => {
    await signOut();
  };

  const getUserInitials = (name?: string, email?: string) => {
    if (name) {
      return name
        .split(" ")
        .map((n) => n[0])
        .join("")
        .toUpperCase()
        .slice(0, 2);
    }
    if (email) {
      return email.slice(0, 2).toUpperCase();
    }
    return "U";
  };

  const maskEmail = (email?: string) => {
    if (!email) return "<EMAIL>";
    const [username, domain] = email.split("@");
    if (username.length <= 2) return email;
    const maskedUsername = username[0] + "*".repeat(username.length - 2) + username[username.length - 1];
    return `${maskedUsername}@${domain}`;
  };

  const getDisplayName = (name?: string) => {
    if (!name) return "User";
    const firstName = name.split(" ")[0];
    return firstName.length > 10 ? firstName.slice(0, 10) + "..." : firstName;
  };

  // Choose logo based on theme for better contrast
  const logoSrc = resolvedTheme === "dark" 
    ? "/images/logos/logo-white.png" 
    : "/images/logos/logo-dark.png";

  return (
    <Sidebar
      collapsible="icon"
      className={`bg-gradient-to-b from-sidebar to-sidebar/98 ${className}`}
    >
      <SidebarHeader className="border-b border-sidebar-border/50 px-2 py-2 bg-gradient-to-r from-primary/8 to-secondary/8">
        <div className="flex items-center justify-between">
          <div className="relative w-16 h-16 group-data-[collapsible=icon]:w-12 group-data-[collapsible=icon]:h-16 transition-all duration-300 ease-in-out">
            <div className="absolute inset-0 bg-gradient-to-br from-primary/15 to-secondary/15 rounded-lg blur-sm opacity-60"></div>
            <Image
              src={logoSrc}
              alt="CSS Dost Logo"
              width={64}
              height={64}
              className="relative w-full h-full object-contain transition-all duration-300 ease-in-out"
            />
          </div>
          <SidebarTrigger
            className="h-8 w-8 hover:bg-gradient-to-r hover:from-primary/20 hover:to-secondary/20 rounded-md transition-all duration-200 ease-in-out"
          />
        </div>
      </SidebarHeader>
      <SidebarContent className="px-4 bg-gradient-to-b from-transparent via-primary/3 to-secondary/3">
        <SidebarGroup className="group-data-[collapsible=icon]:hidden">
          <SidebarGroupLabel className="text-xs font-semibold text-sidebar-foreground/70 uppercase tracking-wider mb-3 flex items-center gap-2">
            <div className="w-1.5 h-1.5 bg-gradient-to-r from-primary to-secondary rounded-full"></div>
            Search
          </SidebarGroupLabel>
          <SidebarInput
            placeholder="Search subjects, tests..."
            className="bg-gradient-to-r from-sidebar-accent/30 to-primary/10 border-sidebar-border/50 focus:bg-gradient-to-r focus:from-sidebar-accent/50 focus:to-primary/15 focus:border-primary/40 rounded-lg transition-all duration-300 shadow-sm focus:shadow-md focus:shadow-primary/20 placeholder:text-sidebar-foreground/50"
          />
        </SidebarGroup>

        <SidebarGroup className="mt-6">
          <SidebarGroupLabel className="text-xs font-semibold text-sidebar-foreground/70 uppercase tracking-wider mb-3 flex items-center gap-2">
            <div className="w-1.5 h-1.5 bg-gradient-to-r from-primary to-secondary rounded-full"></div>
            Navigation
          </SidebarGroupLabel>
          <SidebarMenu className="space-y-1">
            {navigation.map((item) => {
              const isActive = pathname === item.href || pathname.startsWith(item.href);
              return (
                <SidebarMenuItem key={item.href}>
                  <SidebarMenuButton
                    asChild
                    isActive={isActive}
                    className={`
                      relative rounded-lg transition-all duration-300 group
                      ${isActive
                        ? 'bg-gradient-to-r from-primary to-secondary text-primary-foreground shadow-lg shadow-primary/25 border border-primary/20'
                        : 'text-sidebar-foreground hover:text-sidebar-accent-foreground hover:bg-gradient-to-r hover:from-sidebar-accent/40 hover:to-primary/10 hover:shadow-md hover:shadow-primary/15'
                      }
                    `}
                  >
                    <Link href={item.href} className="flex items-center gap-3 px-3 py-2.5">
                      <div className={`p-1.5 rounded-md transition-all duration-300 ${
                        isActive
                          ? 'bg-primary-foreground/20'
                          : 'group-hover:bg-gradient-to-r group-hover:from-primary/20 group-hover:to-secondary/20'
                      }`}>
                        <item.icon className={`w-4 h-4 transition-all duration-300 ${
                          isActive
                            ? 'text-primary-foreground'
                            : 'text-sidebar-foreground/70 group-hover:text-primary'
                        }`} />
                      </div>
                      <span className={`font-medium group-data-[collapsible=icon]:hidden transition-all duration-300 ${
                        isActive ? 'text-primary-foreground font-semibold' : 'group-hover:font-semibold'
                      }`}>
                        {item.title}
                      </span>
                      {isActive && (
                        <div className="absolute right-0 top-1/2 -translate-y-1/2 w-1 h-6 bg-gradient-to-b from-primary-foreground/80 to-primary-foreground/60 rounded-l-full group-data-[collapsible=icon]:hidden" />
                      )}
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              );
            })}
          </SidebarMenu>
        </SidebarGroup>
        <SidebarGroup className="mt-6 group-data-[collapsible=icon]:hidden">
          <SidebarGroupLabel className="text-xs font-semibold text-sidebar-foreground/70 uppercase tracking-wider mb-4 flex items-center gap-2">
            <div className="w-1.5 h-1.5 bg-gradient-to-r from-primary to-secondary rounded-full"></div>
            Quick Stats
          </SidebarGroupLabel>
          <div className="space-y-3">
            {/* Tests Completed Card */}
            <div className="group relative bg-gradient-to-br from-success/20 via-success/15 to-success/8 rounded-xl p-4 border border-success/30 shadow-lg shadow-success/15 hover:shadow-xl hover:shadow-success/25 transition-all duration-300 hover:-translate-y-0.5 cursor-pointer backdrop-blur-sm">
              <div className="absolute inset-0 bg-gradient-to-br from-success/8 to-transparent rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="relative flex items-center gap-3">
                <div className="p-2.5 bg-gradient-to-br from-success/40 to-success/25 rounded-xl shadow-md group-hover:shadow-lg group-hover:shadow-success/30 transition-all duration-300 group-hover:scale-105">
                  <BarChart3 className="w-5 h-5 text-success group-hover:text-success/90 transition-colors duration-300" />
                </div>
                <div className="flex-1">
                  <p className="text-lg font-bold text-sidebar-foreground group-hover:text-success transition-colors duration-300">12</p>
                  <p className="text-xs text-sidebar-foreground/70 font-medium uppercase tracking-wide">Tests Completed</p>
                </div>
                <div className="w-2 h-2 bg-success rounded-full group-hover:scale-125 transition-transform duration-300 shadow-sm"></div>
              </div>
            </div>

            {/* Average Score Card */}
            <div className="group relative bg-gradient-to-br from-secondary/20 via-secondary/15 to-secondary/8 rounded-xl p-4 border border-secondary/30 shadow-lg shadow-secondary/15 hover:shadow-xl hover:shadow-secondary/25 transition-all duration-300 hover:-translate-y-0.5 cursor-pointer backdrop-blur-sm">
              <div className="absolute inset-0 bg-gradient-to-br from-secondary/8 to-transparent rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="relative flex items-center gap-3">
                <div className="p-2.5 bg-gradient-to-br from-secondary/40 to-secondary/25 rounded-xl shadow-md group-hover:shadow-lg group-hover:shadow-secondary/30 transition-all duration-300 group-hover:scale-105">
                  <TrendingUp className="w-5 h-5 text-secondary group-hover:text-secondary/90 transition-colors duration-300" />
                </div>
                <div className="flex-1">
                  <p className="text-lg font-bold text-sidebar-foreground group-hover:text-secondary transition-colors duration-300">78%</p>
                  <p className="text-xs text-sidebar-foreground/70 font-medium uppercase tracking-wide">Average Score</p>
                </div>
                <div className="w-2 h-2 bg-secondary rounded-full group-hover:scale-125 transition-transform duration-300 shadow-sm"></div>
              </div>
            </div>

            {/* Study Streak Card */}
            <div className="group relative bg-gradient-to-br from-warning/20 via-warning/15 to-warning/8 rounded-xl p-4 border border-warning/30 shadow-lg shadow-warning/15 hover:shadow-xl hover:shadow-warning/25 transition-all duration-300 hover:-translate-y-0.5 cursor-pointer backdrop-blur-sm">
              <div className="absolute inset-0 bg-gradient-to-br from-warning/8 to-transparent rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="relative flex items-center gap-3">
                <div className="p-2.5 bg-gradient-to-br from-warning/40 to-warning/25 rounded-xl shadow-md group-hover:shadow-lg group-hover:shadow-warning/30 transition-all duration-300 group-hover:scale-105">
                  <Calendar className="w-5 h-5 text-warning group-hover:text-warning/90 transition-colors duration-300" />
                </div>
                <div className="flex-1">
                  <p className="text-lg font-bold text-sidebar-foreground group-hover:text-warning transition-colors duration-300">7 days</p>
                  <p className="text-xs text-sidebar-foreground/70 font-medium uppercase tracking-wide">Study Streak</p>
                </div>
                <div className="w-2 h-2 bg-warning rounded-full group-hover:scale-125 transition-transform duration-300 shadow-sm"></div>
              </div>
            </div>
          </div>
        </SidebarGroup>
      </SidebarContent>
        <SidebarFooter className="border-t border-sidebar-border/50 p-4 bg-gradient-to-r from-primary/5 to-secondary/5">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="w-full justify-start h-auto p-3 rounded-lg hover:bg-gradient-to-r hover:from-sidebar-accent/40 hover:to-primary/15 transition-all duration-300 group-data-[collapsible=icon]:justify-center hover:shadow-md hover:shadow-primary/20"
              >
                <Avatar className="h-10 w-10 ring-2 ring-gradient-to-r ring-from-primary/30 ring-to-secondary/30 group-data-[collapsible=icon]:h-10 group-data-[collapsible=icon]:w-10 transition-all duration-300 ease-in-out shadow-sm hover:shadow-md">
                  <AvatarImage src={user?.user_metadata?.avatar_url} />
                  <AvatarFallback className="bg-gradient-to-br from-primary to-secondary text-white font-semibold">
                    {getUserInitials(user?.user_metadata?.name, user?.email)}
                  </AvatarFallback>
                </Avatar>
                <div className="ml-3 text-left flex-1 min-w-0 group-data-[collapsible=icon]:hidden">
                  <p className="text-sm font-semibold text-sidebar-foreground truncate">
                    {getDisplayName(user?.user_metadata?.name)}
                  </p>
                  <p className="text-xs text-sidebar-foreground/60 truncate">
                    {maskEmail(user?.email)}
                  </p>
                </div>
                <Settings className="w-4 h-4 text-sidebar-foreground/50 ml-2 group-data-[collapsible=icon]:hidden transition-all duration-300 hover:text-primary hover:rotate-12" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              className="w-56 !bg-card border border-border shadow-xl backdrop-blur-sm z-[100]"
              align="end"
              forceMount
            >
              <DropdownMenuLabel className="font-normal">
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium leading-none text-card-foreground">
                    {user?.user_metadata?.name || "User"}
                  </p>
                  <p className="text-xs leading-none text-muted-foreground">
                    {maskEmail(user?.email)}
                  </p>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="bg-card hover:bg-accent text-card-foreground cursor-pointer">
                <User className="mr-2 h-4 w-4" />
                <span>Profile</span>
              </DropdownMenuItem>
              <DropdownMenuItem className="bg-card hover:bg-accent text-card-foreground cursor-pointer">
                <Target className="mr-2 h-4 w-4" />
                <span>Goals</span>
              </DropdownMenuItem>
              <DropdownMenuItem className="bg-card hover:bg-accent text-card-foreground cursor-pointer">
                <Calendar className="mr-2 h-4 w-4" />
                <span>Schedule</span>
              </DropdownMenuItem>
              <DropdownMenuItem className="bg-card hover:bg-accent text-card-foreground cursor-pointer">
                <Award className="mr-2 h-4 w-4" />
                <span>Achievements</span>
              </DropdownMenuItem>
              <DropdownMenuSeparator className="bg-border" />
              <DropdownMenuItem className="bg-card hover:bg-accent text-card-foreground cursor-pointer">
                <Settings className="mr-2 h-4 w-4" />
                <span>Settings</span>
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={handleSignOut}
                className="bg-card hover:bg-destructive/10 cursor-pointer text-destructive hover:text-destructive/80"
              >
                <LogOut className="mr-2 h-4 w-4" />
                <span>Log out</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </SidebarFooter>
        <SidebarRail />
      </Sidebar>
  );
}
