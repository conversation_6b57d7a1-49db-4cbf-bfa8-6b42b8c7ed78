"use client";

import * as React from "react";
import Link from "next/link";
import Image from "next/image";
import { usePathname } from "next/navigation";
import { useAuth } from "@/components/auth-provider";
import { useTheme } from "@/components/theme-provider";
import {
  LayoutDashboard,
  BookOpen,
  FileText,
  TrendingUp,
  MessageSquare,
  FolderOpen,
  Settings,
  LogOut,
  User,
  Target,
  Calendar,
  Award,
  BarChart3,
} from "lucide-react";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarInput,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
  SidebarTrigger,
} from "@/components/ui/sidebar";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";

interface SidebarProps {
  className?: string;
}

export function DashboardSidebar({ className }: SidebarProps) {
  const pathname = usePathname();
  const { user, signOut } = useAuth();
  const { resolvedTheme } = useTheme();

  const navigation = [
    {
      title: "Dashboard",
      href: "/dashboard",
      icon: LayoutDashboard,
    },
    {
      title: "Subjects",
      href: "/subjects",
      icon: BookOpen,
    },
    {
      title: "Mock Tests",
      href: "/tests",
      icon: FileText,
    },
    {
      title: "Progress",
      href: "/progress",
      icon: TrendingUp,
    },
    {
      title: "Forum",
      href: "/forum",
      icon: MessageSquare,
    },
    {
      title: "Study Materials",
      href: "/materials",
      icon: FolderOpen,
    },
  ];

  const handleSignOut = async () => {
    await signOut();
  };

  const getUserInitials = (name?: string, email?: string) => {
    if (name) {
      return name
        .split(" ")
        .map((n) => n[0])
        .join("")
        .toUpperCase()
        .slice(0, 2);
    }
    if (email) {
      return email.slice(0, 2).toUpperCase();
    }
    return "U";
  };

  const maskEmail = (email?: string) => {
    if (!email) return "<EMAIL>";
    const [username, domain] = email.split("@");
    if (username.length <= 2) return email;
    const maskedUsername = username[0] + "*".repeat(username.length - 2) + username[username.length - 1];
    return `${maskedUsername}@${domain}`;
  };

  const getDisplayName = (name?: string) => {
    if (!name) return "User";
    const firstName = name.split(" ")[0];
    return firstName.length > 10 ? firstName.slice(0, 10) + "..." : firstName;
  };

  // Choose logo based on theme for better contrast
  const logoSrc = resolvedTheme === "dark" 
    ? "/images/logos/logo-white.png" 
    : "/images/logos/logo-dark.png";

  return (
    <Sidebar
      collapsible="icon"
      className={`bg-gradient-to-br from-sidebar via-sidebar/98 to-sidebar-accent/80 backdrop-blur-sm ${className}`}
    >
      <SidebarHeader className="border-b border-sidebar-border/50 px-2 py-2 bg-gradient-to-r from-sidebar-primary/10 to-secondary/10">
        <div className="flex items-center justify-between">
          <div className="relative w-16 h-16 group-data-[collapsible=icon]:w-12 group-data-[collapsible=icon]:h-16 transition-all duration-300 ease-in-out">
            <div className="absolute inset-0 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-xl blur-sm opacity-50"></div>
            <Image
              src={logoSrc}
              alt="CSS Dost Logo"
              width={64}
              height={64}
              className="relative w-full h-full object-contain transition-all duration-300 ease-in-out drop-shadow-lg"
            />
          </div>
          <SidebarTrigger
            className="h-8 w-8 hover:bg-gradient-to-r hover:from-sidebar-primary/20 hover:to-secondary/20 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md"
          />
        </div>
      </SidebarHeader>
      <SidebarContent className="px-4 bg-gradient-to-b from-transparent via-sidebar-primary/5 to-secondary/5">
        <SidebarGroup className="group-data-[collapsible=icon]:hidden">
          <SidebarGroupLabel className="text-xs font-semibold text-sidebar-foreground/70 uppercase tracking-wider mb-2 flex items-center gap-2">
            <div className="w-2 h-2 bg-gradient-to-r from-primary to-secondary rounded-full"></div>
            Search
          </SidebarGroupLabel>
          <SidebarInput
            placeholder="Search subjects, tests..."
            className="bg-gradient-to-r from-sidebar-accent/30 to-sidebar-primary/20 border-sidebar-border/50 focus:bg-gradient-to-r focus:from-sidebar-accent/50 focus:to-sidebar-primary/30 focus:border-sidebar-primary/50 transition-all duration-200"
          />
        </SidebarGroup>

        <SidebarGroup className="mt-6">
          <SidebarGroupLabel className="text-xs font-semibold text-sidebar-foreground/70 uppercase tracking-wider mb-2 flex items-center gap-2">
            <div className="w-2 h-2 bg-gradient-to-r from-primary to-secondary rounded-full"></div>
            Navigation
          </SidebarGroupLabel>
          <SidebarMenu className="space-y-2">
            {navigation.map((item) => {
              const isActive = pathname === item.href || pathname.startsWith(item.href);
              return (
                <SidebarMenuItem key={item.href}>
                  <SidebarMenuButton
                    asChild
                    isActive={isActive}
                    className={`
                      relative rounded-xl transition-all duration-300 ease-in-out group overflow-hidden
                      ${isActive
                        ? 'bg-gradient-to-r from-primary to-secondary text-white shadow-xl shadow-primary/25 border border-primary/30 scale-[1.02]'
                        : 'text-sidebar-foreground hover:text-sidebar-accent-foreground hover:bg-gradient-to-r hover:from-sidebar-accent/30 hover:to-sidebar-primary/20 hover:shadow-lg hover:scale-[1.01]'
                      }
                    `}
                  >
                    <Link href={item.href} className="flex items-center gap-3 px-3 py-3 relative z-10">
                      <div className={`p-2 rounded-lg transition-all duration-300 ${
                        isActive
                          ? 'bg-white/20 shadow-lg backdrop-blur-sm'
                          : 'bg-sidebar-accent/20 group-hover:bg-sidebar-primary/20'
                      }`}>
                        <item.icon className={`w-5 h-5 transition-all duration-300 ${
                          isActive
                            ? 'text-white drop-shadow-sm'
                            : 'text-sidebar-foreground/70 group-hover:text-sidebar-primary'
                        }`} />
                      </div>
                      <span className={`font-medium group-data-[collapsible=icon]:hidden transition-all duration-300 ease-in-out ${
                        isActive
                          ? 'text-white font-semibold drop-shadow-sm'
                          : 'group-hover:font-semibold'
                      }`}>
                        {item.title}
                      </span>
                      {isActive && (
                        <>
                          <div className="absolute right-0 top-1/2 -translate-y-1/2 w-1 h-8 bg-gradient-to-b from-white/80 to-white/60 rounded-l-full group-data-[collapsible=icon]:hidden transition-all duration-300 ease-in-out shadow-lg" />
                          <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent rounded-xl opacity-50"></div>
                        </>
                      )}
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              );
            })}
          </SidebarMenu>
        </SidebarGroup>
        <SidebarGroup className="mt-6 group-data-[collapsible=icon]:hidden">
          <SidebarGroupLabel className="text-xs font-semibold text-sidebar-foreground/70 uppercase tracking-wider mb-3 flex items-center gap-2">
            <div className="w-2 h-2 bg-gradient-to-r from-primary to-secondary rounded-full"></div>
            Quick Stats
          </SidebarGroupLabel>
          <div className="space-y-3">
            <div className="bg-gradient-to-br from-success/20 via-success/15 to-success/10 rounded-xl p-3 border border-success/30 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02] backdrop-blur-sm">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-gradient-to-br from-success/30 to-success/20 rounded-xl shadow-md">
                  <BarChart3 className="w-4 h-4 text-success drop-shadow-sm" />
                </div>
                <div>
                  <p className="text-sm font-bold text-sidebar-foreground drop-shadow-sm">12</p>
                  <p className="text-xs text-sidebar-foreground/70 font-medium">Tests Completed</p>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-br from-secondary/20 via-secondary/15 to-secondary/10 rounded-xl p-3 border border-secondary/30 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02] backdrop-blur-sm">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-gradient-to-br from-secondary/30 to-secondary/20 rounded-xl shadow-md">
                  <TrendingUp className="w-4 h-4 text-secondary drop-shadow-sm" />
                </div>
                <div>
                  <p className="text-sm font-bold text-sidebar-foreground drop-shadow-sm">78%</p>
                  <p className="text-xs text-sidebar-foreground/70 font-medium">Average Score</p>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-br from-warning/20 via-warning/15 to-warning/10 rounded-xl p-3 border border-warning/30 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02] backdrop-blur-sm">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-gradient-to-br from-warning/30 to-warning/20 rounded-xl shadow-md">
                  <Calendar className="w-4 h-4 text-warning drop-shadow-sm" />
                </div>
                <div>
                  <p className="text-sm font-bold text-sidebar-foreground drop-shadow-sm">7 days</p>
                  <p className="text-xs text-sidebar-foreground/70 font-medium">Study Streak</p>
                </div>
              </div>
            </div>
          </div>
        </SidebarGroup>
      </SidebarContent>
        <SidebarFooter className="border-t border-sidebar-border/50 p-4 bg-gradient-to-r from-sidebar-primary/10 to-secondary/10">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="w-full justify-start h-auto p-3 rounded-xl hover:bg-gradient-to-r hover:from-sidebar-accent/30 hover:to-sidebar-primary/20 transition-all duration-300 group-data-[collapsible=icon]:justify-center hover:shadow-lg hover:scale-[1.02] backdrop-blur-sm"
              >
                <Avatar className="h-10 w-10 ring-2 ring-gradient-to-r ring-primary/30 group-data-[collapsible=icon]:h-10 group-data-[collapsible=icon]:w-10 transition-all duration-300 ease-in-out shadow-lg">
                  <AvatarImage src={user?.user_metadata?.avatar_url} />
                  <AvatarFallback className="bg-gradient-to-br from-primary via-primary/90 to-secondary text-white font-bold shadow-inner">
                    {getUserInitials(user?.user_metadata?.name, user?.email)}
                  </AvatarFallback>
                </Avatar>
                <div className="ml-3 text-left flex-1 min-w-0 group-data-[collapsible=icon]:hidden">
                  <p className="text-sm font-bold text-sidebar-foreground truncate drop-shadow-sm">
                    {getDisplayName(user?.user_metadata?.name)}
                  </p>
                  <p className="text-xs text-sidebar-foreground/70 truncate font-medium">
                    {maskEmail(user?.email)}
                  </p>
                </div>
                <Settings className="w-4 h-4 text-sidebar-foreground/50 ml-2 group-data-[collapsible=icon]:hidden transition-all duration-300 hover:text-sidebar-primary hover:rotate-90" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              className="w-56 !bg-gradient-to-br !from-card !via-card/95 !to-card/90 border border-border/50 shadow-2xl backdrop-blur-md z-[100] rounded-xl"
              align="end"
              forceMount
            >
              <DropdownMenuLabel className="font-normal p-3 bg-gradient-to-r from-primary/10 to-secondary/10 rounded-t-xl">
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-bold leading-none text-card-foreground drop-shadow-sm">
                    {user?.user_metadata?.name || "User"}
                  </p>
                  <p className="text-xs leading-none text-muted-foreground font-medium">
                    {maskEmail(user?.email)}
                  </p>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator className="bg-gradient-to-r from-border/50 via-border to-border/50" />
              <DropdownMenuItem className="bg-card hover:bg-gradient-to-r hover:from-primary/10 hover:to-secondary/10 text-card-foreground cursor-pointer transition-all duration-200 hover:shadow-md rounded-lg mx-1 my-1">
                <User className="mr-2 h-4 w-4 text-primary" />
                <span className="font-medium">Profile</span>
              </DropdownMenuItem>
              <DropdownMenuItem className="bg-card hover:bg-gradient-to-r hover:from-primary/10 hover:to-secondary/10 text-card-foreground cursor-pointer transition-all duration-200 hover:shadow-md rounded-lg mx-1 my-1">
                <Target className="mr-2 h-4 w-4 text-secondary" />
                <span className="font-medium">Goals</span>
              </DropdownMenuItem>
              <DropdownMenuItem className="bg-card hover:bg-gradient-to-r hover:from-primary/10 hover:to-secondary/10 text-card-foreground cursor-pointer transition-all duration-200 hover:shadow-md rounded-lg mx-1 my-1">
                <Calendar className="mr-2 h-4 w-4 text-warning" />
                <span className="font-medium">Schedule</span>
              </DropdownMenuItem>
              <DropdownMenuItem className="bg-card hover:bg-gradient-to-r hover:from-primary/10 hover:to-secondary/10 text-card-foreground cursor-pointer transition-all duration-200 hover:shadow-md rounded-lg mx-1 my-1">
                <Award className="mr-2 h-4 w-4 text-success" />
                <span className="font-medium">Achievements</span>
              </DropdownMenuItem>
              <DropdownMenuSeparator className="bg-gradient-to-r from-border/50 via-border to-border/50 my-2" />
              <DropdownMenuItem className="bg-card hover:bg-gradient-to-r hover:from-primary/10 hover:to-secondary/10 text-card-foreground cursor-pointer transition-all duration-200 hover:shadow-md rounded-lg mx-1 my-1">
                <Settings className="mr-2 h-4 w-4 text-muted-foreground" />
                <span className="font-medium">Settings</span>
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={handleSignOut}
                className="bg-card hover:bg-gradient-to-r hover:from-destructive/10 hover:to-destructive/20 cursor-pointer text-destructive hover:text-destructive/90 transition-all duration-200 hover:shadow-md rounded-lg mx-1 my-1 font-medium"
              >
                <LogOut className="mr-2 h-4 w-4" />
                <span>Log out</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </SidebarFooter>
        <SidebarRail />
      </Sidebar>
  );
}
