"use client";

import * as React from "react";
import Link from "next/link";
import Image from "next/image";
import { usePathname } from "next/navigation";
import { useAuth } from "@/components/auth-provider";
import { useTheme } from "@/components/theme-provider";
import {
  LayoutDashboard,
  BookOpen,
  FileText,
  TrendingUp,
  MessageSquare,
  FolderOpen,
  Settings,
  LogOut,
  User,
  Target,
  Calendar,
  Award,
  BarChart3,
} from "lucide-react";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarInput,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
  SidebarTrigger,
} from "@/components/ui/sidebar";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";

interface SidebarProps {
  className?: string;
}

export function DashboardSidebar({ className }: SidebarProps) {
  const pathname = usePathname();
  const { user, signOut } = useAuth();
  const { resolvedTheme } = useTheme();

  const navigation = [
    {
      title: "Dashboard",
      href: "/dashboard",
      icon: LayoutDashboard,
    },
    {
      title: "Subjects",
      href: "/subjects",
      icon: BookOpen,
    },
    {
      title: "Mock Tests",
      href: "/tests",
      icon: FileText,
    },
    {
      title: "Progress",
      href: "/progress",
      icon: TrendingUp,
    },
    {
      title: "Forum",
      href: "/forum",
      icon: MessageSquare,
    },
    {
      title: "Study Materials",
      href: "/materials",
      icon: FolderOpen,
    },
  ];

  const handleSignOut = async () => {
    await signOut();
  };

  const getUserInitials = (name?: string, email?: string) => {
    if (name) {
      return name
        .split(" ")
        .map((n) => n[0])
        .join("")
        .toUpperCase()
        .slice(0, 2);
    }
    if (email) {
      return email.slice(0, 2).toUpperCase();
    }
    return "U";
  };

  const maskEmail = (email?: string) => {
    if (!email) return "<EMAIL>";
    const [username, domain] = email.split("@");
    if (username.length <= 2) return email;
    const maskedUsername = username[0] + "*".repeat(username.length - 2) + username[username.length - 1];
    return `${maskedUsername}@${domain}`;
  };

  const getDisplayName = (name?: string) => {
    if (!name) return "User";
    const firstName = name.split(" ")[0];
    return firstName.length > 10 ? firstName.slice(0, 10) + "..." : firstName;
  };

  // Choose logo based on theme for better contrast
  const logoSrc = resolvedTheme === "dark" 
    ? "/images/logos/logo-white.png" 
    : "/images/logos/logo-dark.png";

  return (
    <Sidebar
      collapsible="icon"
      className={`bg-gradient-to-b from-sidebar/98 via-sidebar/95 to-sidebar/98 backdrop-blur-xl border-r border-sidebar-border/50 ${className}`}
    >
      <SidebarHeader className="border-b border-sidebar-border/30 px-4 py-4 bg-gradient-to-r from-primary/10 via-secondary/8 to-primary/10">
        <div className="flex items-center justify-between">
          <div className="relative w-16 h-16 group-data-[collapsible=icon]:w-12 group-data-[collapsible=icon]:h-16 transition-all duration-300 ease-in-out">
            <div className="absolute inset-0 bg-gradient-to-br from-primary/20 via-secondary/15 to-primary/20 rounded-xl blur-md opacity-70 animate-pulse"></div>
            <div className="absolute inset-0 bg-gradient-to-tr from-secondary/10 to-primary/10 rounded-xl"></div>
            <Image
              src={logoSrc}
              alt="CSS Dost Logo"
              width={64}
              height={64}
              className="relative w-full h-full object-contain transition-all duration-300 ease-in-out drop-shadow-lg"
            />
          </div>
          <SidebarTrigger
            className="h-8 w-8 hover:bg-gradient-to-r hover:from-primary/20 hover:to-secondary/20 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:scale-105"
          />
        </div>
      </SidebarHeader>
      <SidebarContent className="px-4 py-2 bg-gradient-to-b from-transparent via-primary/2 to-secondary/2">
        <SidebarGroup className="group-data-[collapsible=icon]:hidden">
          <SidebarGroupLabel className="text-xs font-bold text-sidebar-foreground/80 uppercase tracking-wider mb-3 px-2 flex items-center gap-2">
            <div className="w-2 h-2 bg-gradient-to-r from-violet-500 to-purple-500 rounded-full animate-pulse"></div>
            Search
          </SidebarGroupLabel>
          <div className="relative">
            <SidebarInput
              placeholder="Search subjects, tests..."
              className="bg-gradient-to-r from-violet-500/10 via-purple-500/5 to-violet-500/10 border-violet-400/30 focus:bg-gradient-to-r focus:from-violet-500/20 focus:via-purple-500/15 focus:to-violet-500/20 focus:border-violet-400/50 rounded-lg transition-all duration-300 placeholder:text-sidebar-foreground/60 shadow-sm focus:shadow-md focus:shadow-violet-500/20"
            />
            <div className="absolute inset-0 bg-gradient-to-r from-violet-400/5 to-purple-400/5 rounded-lg pointer-events-none"></div>
          </div>
        </SidebarGroup>

        <SidebarGroup className="mt-6">
          <SidebarGroupLabel className="text-xs font-bold text-sidebar-foreground/80 uppercase tracking-wider mb-3 px-2 flex items-center gap-2">
            <div className="w-2 h-2 bg-gradient-to-r from-primary to-secondary rounded-full animate-pulse"></div>
            Navigation
          </SidebarGroupLabel>
          <SidebarMenu className="space-y-1.5">
            {navigation.map((item, index) => {
              const isActive = pathname === item.href || pathname.startsWith(item.href);

              // Define vibrant colors for each navigation item
              const colors = [
                { bg: 'from-purple-500/20 to-indigo-500/10', border: 'border-purple-400/30', icon: 'bg-purple-500/30', iconColor: 'text-purple-600', shadow: 'shadow-purple-500/20' },
                { bg: 'from-emerald-500/20 to-green-500/10', border: 'border-emerald-400/30', icon: 'bg-emerald-500/30', iconColor: 'text-emerald-600', shadow: 'shadow-emerald-500/20' },
                { bg: 'from-blue-500/20 to-cyan-500/10', border: 'border-blue-400/30', icon: 'bg-blue-500/30', iconColor: 'text-blue-600', shadow: 'shadow-blue-500/20' },
                { bg: 'from-rose-500/20 to-pink-500/10', border: 'border-rose-400/30', icon: 'bg-rose-500/30', iconColor: 'text-rose-600', shadow: 'shadow-rose-500/20' },
                { bg: 'from-amber-500/20 to-orange-500/10', border: 'border-amber-400/30', icon: 'bg-amber-500/30', iconColor: 'text-amber-600', shadow: 'shadow-amber-500/20' },
                { bg: 'from-teal-500/20 to-cyan-500/10', border: 'border-teal-400/30', icon: 'bg-teal-500/30', iconColor: 'text-teal-600', shadow: 'shadow-teal-500/20' },
              ];

              const itemColor = colors[index % colors.length];

              return (
                <SidebarMenuItem key={item.href}>
                  <SidebarMenuButton
                    asChild
                    isActive={isActive}
                    className={`
                      relative rounded-xl transition-all duration-300 group overflow-hidden
                      ${isActive
                        ? `bg-gradient-to-r from-primary to-secondary text-primary-foreground shadow-xl shadow-primary/30 border border-primary/20 scale-[1.02]`
                        : `text-sidebar-foreground hover:text-sidebar-accent-foreground hover:bg-gradient-to-r hover:${itemColor.bg} hover:${itemColor.border} hover:${itemColor.shadow} hover:shadow-md hover:scale-[1.01]`
                      }
                    `}
                  >
                    <Link href={item.href} className="flex items-center gap-3 px-3 py-3 relative z-10">
                      <div className={`p-2 rounded-lg transition-all duration-300 shadow-sm ${
                        isActive
                          ? 'bg-primary-foreground/20 shadow-lg'
                          : `${itemColor.icon} group-hover:shadow-md`
                      }`}>
                        <item.icon className={`w-4 h-4 transition-all duration-300 ${
                          isActive
                            ? 'text-primary-foreground'
                            : `${itemColor.iconColor} group-hover:scale-110`
                        }`} />
                      </div>
                      <span className={`font-medium group-data-[collapsible=icon]:hidden transition-all duration-300 ${
                        isActive ? 'text-primary-foreground font-bold' : 'group-hover:font-semibold'
                      }`}>
                        {item.title}
                      </span>
                      {isActive && (
                        <>
                          <div className="absolute right-0 top-1/2 -translate-y-1/2 w-1 h-8 bg-gradient-to-b from-primary-foreground/80 to-primary-foreground/60 rounded-l-full group-data-[collapsible=icon]:hidden shadow-lg" />
                          <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent rounded-xl"></div>
                        </>
                      )}
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              );
            })}
          </SidebarMenu>
        </SidebarGroup>
        <SidebarGroup className="mt-6 group-data-[collapsible=icon]:hidden">
          <SidebarGroupLabel className="text-xs font-bold text-sidebar-foreground/80 uppercase tracking-wider mb-4 px-2 flex items-center gap-2">
            <div className="w-2 h-2 bg-gradient-to-r from-primary to-secondary rounded-full animate-pulse"></div>
            Quick Stats
          </SidebarGroupLabel>
          <div className="space-y-3">
            {/* Tests Completed - Vibrant Green */}
            <div className="relative overflow-hidden bg-gradient-to-br from-emerald-500/20 via-green-500/15 to-emerald-600/10 rounded-xl p-4 border border-emerald-400/30 hover:border-emerald-400/50 transition-all duration-300 hover:shadow-lg hover:shadow-emerald-500/20 hover:scale-[1.02] group cursor-pointer">
              <div className="absolute inset-0 bg-gradient-to-br from-emerald-400/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="relative flex items-center gap-3">
                <div className="p-2.5 bg-gradient-to-br from-emerald-500/30 to-green-500/20 rounded-xl shadow-lg group-hover:shadow-emerald-500/30 transition-all duration-300">
                  <BarChart3 className="w-5 h-5 text-emerald-600 group-hover:text-emerald-500 transition-colors duration-300" />
                </div>
                <div className="flex-1">
                  <p className="text-lg font-bold text-sidebar-foreground group-hover:text-emerald-700 transition-colors duration-300">12</p>
                  <p className="text-xs text-sidebar-foreground/70 font-semibold uppercase tracking-wide">Tests Completed</p>
                </div>
                <div className="w-8 h-8 bg-emerald-500/20 rounded-full flex items-center justify-center group-hover:bg-emerald-500/30 transition-all duration-300">
                  <div className="w-2 h-2 bg-emerald-500 rounded-full group-hover:scale-125 transition-transform duration-300"></div>
                </div>
              </div>
            </div>

            {/* Average Score - Vibrant Blue */}
            <div className="relative overflow-hidden bg-gradient-to-br from-blue-500/20 via-cyan-500/15 to-blue-600/10 rounded-xl p-4 border border-blue-400/30 hover:border-blue-400/50 transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/20 hover:scale-[1.02] group cursor-pointer">
              <div className="absolute inset-0 bg-gradient-to-br from-blue-400/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="relative flex items-center gap-3">
                <div className="p-2.5 bg-gradient-to-br from-blue-500/30 to-cyan-500/20 rounded-xl shadow-lg group-hover:shadow-blue-500/30 transition-all duration-300">
                  <TrendingUp className="w-5 h-5 text-blue-600 group-hover:text-blue-500 transition-colors duration-300" />
                </div>
                <div className="flex-1">
                  <p className="text-lg font-bold text-sidebar-foreground group-hover:text-blue-700 transition-colors duration-300">78%</p>
                  <p className="text-xs text-sidebar-foreground/70 font-semibold uppercase tracking-wide">Average Score</p>
                </div>
                <div className="w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center group-hover:bg-blue-500/30 transition-all duration-300">
                  <div className="w-2 h-2 bg-blue-500 rounded-full group-hover:scale-125 transition-transform duration-300"></div>
                </div>
              </div>
            </div>

            {/* Study Streak - Vibrant Orange */}
            <div className="relative overflow-hidden bg-gradient-to-br from-orange-500/20 via-amber-500/15 to-orange-600/10 rounded-xl p-4 border border-orange-400/30 hover:border-orange-400/50 transition-all duration-300 hover:shadow-lg hover:shadow-orange-500/20 hover:scale-[1.02] group cursor-pointer">
              <div className="absolute inset-0 bg-gradient-to-br from-orange-400/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="relative flex items-center gap-3">
                <div className="p-2.5 bg-gradient-to-br from-orange-500/30 to-amber-500/20 rounded-xl shadow-lg group-hover:shadow-orange-500/30 transition-all duration-300">
                  <Calendar className="w-5 h-5 text-orange-600 group-hover:text-orange-500 transition-colors duration-300" />
                </div>
                <div className="flex-1">
                  <p className="text-lg font-bold text-sidebar-foreground group-hover:text-orange-700 transition-colors duration-300">7 days</p>
                  <p className="text-xs text-sidebar-foreground/70 font-semibold uppercase tracking-wide">Study Streak</p>
                </div>
                <div className="w-8 h-8 bg-orange-500/20 rounded-full flex items-center justify-center group-hover:bg-orange-500/30 transition-all duration-300">
                  <div className="w-2 h-2 bg-orange-500 rounded-full group-hover:scale-125 transition-transform duration-300"></div>
                </div>
              </div>
            </div>
          </div>
        </SidebarGroup>
      </SidebarContent>
        <SidebarFooter className="border-t border-sidebar-border/30 p-4 bg-sidebar-primary/5">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="w-full justify-start h-auto p-3 rounded-lg hover:bg-sidebar-accent/50 transition-all duration-200 group-data-[collapsible=icon]:justify-center hover:shadow-sm"
              >
                <Avatar className="h-10 w-10 ring-2 ring-primary/20 group-data-[collapsible=icon]:h-10 group-data-[collapsible=icon]:w-10 transition-all duration-300 ease-in-out shadow-sm">
                  <AvatarImage src={user?.user_metadata?.avatar_url} />
                  <AvatarFallback className="bg-gradient-to-br from-primary to-secondary text-white font-semibold">
                    {getUserInitials(user?.user_metadata?.name, user?.email)}
                  </AvatarFallback>
                </Avatar>
                <div className="ml-3 text-left flex-1 min-w-0 group-data-[collapsible=icon]:hidden">
                  <p className="text-sm font-semibold text-sidebar-foreground truncate">
                    {getDisplayName(user?.user_metadata?.name)}
                  </p>
                  <p className="text-xs text-sidebar-foreground/60 truncate font-medium">
                    {maskEmail(user?.email)}
                  </p>
                </div>
                <Settings className="w-4 h-4 text-sidebar-foreground/50 ml-2 group-data-[collapsible=icon]:hidden transition-colors duration-200 hover:text-sidebar-primary" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              className="w-56 !bg-card/95 border border-border/50 shadow-xl backdrop-blur-md z-[100] rounded-lg"
              align="end"
              forceMount
            >
              <DropdownMenuLabel className="font-normal p-3 bg-primary/5 rounded-t-lg">
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-semibold leading-none text-card-foreground">
                    {user?.user_metadata?.name || "User"}
                  </p>
                  <p className="text-xs leading-none text-muted-foreground font-medium">
                    {maskEmail(user?.email)}
                  </p>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator className="bg-border/50" />
              <DropdownMenuItem className="bg-card hover:bg-primary/10 text-card-foreground cursor-pointer transition-all duration-200 rounded-md mx-1 my-0.5">
                <User className="mr-2 h-4 w-4 text-primary" />
                <span className="font-medium">Profile</span>
              </DropdownMenuItem>
              <DropdownMenuItem className="bg-card hover:bg-secondary/10 text-card-foreground cursor-pointer transition-all duration-200 rounded-md mx-1 my-0.5">
                <Target className="mr-2 h-4 w-4 text-secondary" />
                <span className="font-medium">Goals</span>
              </DropdownMenuItem>
              <DropdownMenuItem className="bg-card hover:bg-warning/10 text-card-foreground cursor-pointer transition-all duration-200 rounded-md mx-1 my-0.5">
                <Calendar className="mr-2 h-4 w-4 text-warning" />
                <span className="font-medium">Schedule</span>
              </DropdownMenuItem>
              <DropdownMenuItem className="bg-card hover:bg-success/10 text-card-foreground cursor-pointer transition-all duration-200 rounded-md mx-1 my-0.5">
                <Award className="mr-2 h-4 w-4 text-success" />
                <span className="font-medium">Achievements</span>
              </DropdownMenuItem>
              <DropdownMenuSeparator className="bg-border/50 my-1" />
              <DropdownMenuItem className="bg-card hover:bg-accent text-card-foreground cursor-pointer transition-all duration-200 rounded-md mx-1 my-0.5">
                <Settings className="mr-2 h-4 w-4 text-muted-foreground" />
                <span className="font-medium">Settings</span>
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={handleSignOut}
                className="bg-card hover:bg-destructive/10 cursor-pointer text-destructive hover:text-destructive/90 transition-all duration-200 rounded-md mx-1 my-0.5 font-medium"
              >
                <LogOut className="mr-2 h-4 w-4" />
                <span>Log out</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </SidebarFooter>
        <SidebarRail />
      </Sidebar>
  );
}
