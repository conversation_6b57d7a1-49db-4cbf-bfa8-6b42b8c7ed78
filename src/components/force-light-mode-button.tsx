"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Sun } from "lucide-react";
import { forceLightMode } from "@/lib/theme-utils";

/**
 * Emergency button to force light mode
 * Add this temporarily to your app to force reset theme
 */
export function ForceLightModeButton() {
  const handleForceLight = () => {
    console.log("🌞 Forcing light mode...");
    
    // Clear all possible theme storage keys
    localStorage.removeItem("cssdost-theme");
    localStorage.removeItem("theme");
    localStorage.removeItem("ui-theme");
    
    // Set light mode explicitly
    localStorage.setItem("cssdost-theme", "light");
    
    // Update DOM classes
    document.documentElement.classList.remove("dark");
    document.documentElement.classList.add("light");
    
    // Force reload
    window.location.reload();
  };

  return (
    <Button 
      onClick={handleForceLight}
      variant="outline"
      size="sm"
      className="fixed top-4 right-4 z-50 bg-yellow-100 hover:bg-yellow-200 border-yellow-300"
    >
      <Sun className="w-4 h-4 mr-2" />
      Force Light Mode
    </Button>
  );
}
